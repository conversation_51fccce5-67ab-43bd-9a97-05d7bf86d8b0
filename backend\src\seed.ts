import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create demo company
  const company = await prisma.company.create({
    data: {
      name: 'TalentSol Demo Company',
      domain: 'talentsol-demo.com',
    },
  });

  console.log('✅ Created demo company');

  // Create demo users
  const passwordHash = await bcrypt.hash('password123', 12);

  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      passwordHash,
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      companyId: company.id,
    },
  });

  const recruiterUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      passwordHash,
      firstName: 'Jane',
      lastName: 'Recruiter',
      role: 'recruiter',
      companyId: company.id,
    },
  });

  console.log('✅ Created demo users');

  // Create demo jobs
  const jobs = await Promise.all([
    prisma.job.create({
      data: {
        title: 'Senior Frontend Developer',
        department: 'Engineering',
        location: {
          city: 'San Francisco',
          state: 'CA',
          country: 'USA',
        },
        employmentType: 'full-time',
        experienceLevel: 'senior',
        salary: {
          min: 120000,
          max: 150000,
          currency: 'USD',
          negotiable: true,
        },
        description: 'We are looking for a Senior Frontend Developer to join our growing team...',
        responsibilities: [
          'Develop and maintain React applications',
          'Collaborate with design and backend teams',
          'Write clean, maintainable code',
          'Mentor junior developers',
        ],
        requiredQualifications: [
          '5+ years of React experience',
          'Strong TypeScript skills',
          'Experience with modern build tools',
        ],
        preferredQualifications: [
          'Experience with Next.js',
          'Knowledge of GraphQL',
          'Previous startup experience',
        ],
        skills: ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML'],
        benefits: 'Health insurance, 401k, flexible PTO, remote work options',
        status: 'open',
        visibility: 'public',
        postedDate: new Date(),
        maxApplicants: 50,
        currentApplicants: 0,
        pipeline: {
          stages: ['applied', 'screening', 'interview', 'assessment', 'offer', 'hired'],
        },
        createdById: adminUser.id,
        companyId: company.id,
      },
    }),
    prisma.job.create({
      data: {
        title: 'UX Designer',
        department: 'Design',
        location: {
          city: 'New York',
          state: 'NY',
          country: 'USA',
        },
        employmentType: 'full-time',
        experienceLevel: 'mid',
        salary: {
          min: 80000,
          max: 110000,
          currency: 'USD',
          negotiable: true,
        },
        description: 'Join our design team to create amazing user experiences...',
        responsibilities: [
          'Design user interfaces and experiences',
          'Conduct user research',
          'Create wireframes and prototypes',
          'Collaborate with product and engineering teams',
        ],
        requiredQualifications: [
          '3+ years of UX design experience',
          'Proficiency in Figma',
          'Strong portfolio',
        ],
        preferredQualifications: [
          'Experience with design systems',
          'Knowledge of front-end development',
          'Previous B2B product experience',
        ],
        skills: ['Figma', 'Sketch', 'Adobe Creative Suite', 'Prototyping', 'User Research'],
        benefits: 'Health insurance, 401k, flexible PTO, design conference budget',
        status: 'open',
        visibility: 'public',
        postedDate: new Date(),
        maxApplicants: 30,
        currentApplicants: 0,
        pipeline: {
          stages: ['applied', 'screening', 'interview', 'assessment', 'offer', 'hired'],
        },
        createdById: recruiterUser.id,
        companyId: company.id,
      },
    }),
    prisma.job.create({
      data: {
        title: 'Backend Engineer',
        department: 'Engineering',
        location: {
          city: 'Austin',
          state: 'TX',
          country: 'USA',
        },
        employmentType: 'full-time',
        experienceLevel: 'mid',
        salary: {
          min: 100000,
          max: 130000,
          currency: 'USD',
          negotiable: false,
        },
        description: 'Build scalable backend systems and APIs...',
        responsibilities: [
          'Design and implement APIs',
          'Optimize database performance',
          'Ensure system scalability',
          'Write comprehensive tests',
        ],
        requiredQualifications: [
          '3+ years of backend development',
          'Experience with Node.js or Python',
          'Database design experience',
        ],
        preferredQualifications: [
          'Experience with microservices',
          'Cloud platform experience (AWS/GCP)',
          'DevOps knowledge',
        ],
        skills: ['Node.js', 'Python', 'PostgreSQL', 'MongoDB', 'Docker', 'AWS'],
        benefits: 'Health insurance, 401k, flexible PTO, learning budget',
        status: 'open',
        visibility: 'public',
        postedDate: new Date(),
        maxApplicants: 40,
        currentApplicants: 0,
        pipeline: {
          stages: ['applied', 'screening', 'interview', 'assessment', 'offer', 'hired'],
        },
        createdById: adminUser.id,
        companyId: company.id,
      },
    }),
  ]);

  console.log('✅ Created demo jobs');

  // Create demo candidates and applications
  const candidates = [
    {
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+****************',
      location: { city: 'San Francisco', state: 'CA', country: 'USA' },
      workAuthorization: 'US Citizen',
      linkedinUrl: 'https://linkedin.com/in/johnsmith',
    },
    {
      firstName: 'Emma',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      location: { city: 'New York', state: 'NY', country: 'USA' },
      workAuthorization: 'Work Visa',
      portfolioUrl: 'https://emmajohnson.design',
    },
    {
      firstName: 'Michael',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '+****************',
      location: { city: 'Austin', state: 'TX', country: 'USA' },
      workAuthorization: 'US Citizen',
    },
  ];

  for (let i = 0; i < candidates.length; i++) {
    const candidateData = candidates[i];
    const job = jobs[i % jobs.length];

    const candidate = await prisma.candidate.create({
      data: candidateData,
    });

    await prisma.application.create({
      data: {
        jobId: job.id,
        candidateId: candidate.id,
        status: ['applied', 'screening', 'interview'][i % 3] as any,
        submittedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random date within last week
        candidateInfo: candidateData,
        professionalInfo: {
          experience: ['3-5', '1-3', '5-10'][i % 3],
          expectedSalary: {
            min: 80000 + i * 10000,
            max: 120000 + i * 15000,
            currency: 'USD',
            negotiable: true,
          },
          noticePeriod: '2 weeks',
          remoteWork: true,
        },
        metadata: {
          source: ['company_website', 'job_board', 'referral'][i % 3] as any,
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0 (compatible browser)',
          formVersion: '1.0',
          completionTime: 300 + Math.random() * 600,
          gdprConsent: true,
          marketingConsent: false,
        },
        activity: [
          {
            type: 'application_submitted',
            timestamp: new Date().toISOString(),
            description: 'Application submitted',
          },
        ],
      },
    });

    // Update job application count
    await prisma.job.update({
      where: { id: job.id },
      data: {
        currentApplicants: {
          increment: 1,
        },
      },
    });
  }

  console.log('✅ Created demo candidates and applications');

  // Create email templates
  await prisma.emailTemplate.create({
    data: {
      name: 'Application Confirmation',
      subject: 'Thank you for your application to {{jobTitle}}',
      body: `Dear {{candidateName}},

Thank you for applying to the {{jobTitle}} position at {{companyName}}. We have received your application and will review it shortly.

We will contact you within the next few days if your qualifications match our requirements.

Best regards,
The {{companyName}} Team`,
      type: 'confirmation',
      variables: ['candidateName', 'jobTitle', 'companyName'],
    },
  });

  console.log('✅ Created email templates');

  console.log('🎉 Database seed completed successfully!');
  console.log('\n📧 Demo login credentials:');
  console.log('Admin: <EMAIL> / password123');
  console.log('Recruiter: <EMAIL> / password123');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
